# Solusi <PERSON>k<PERSON>si Checkbox dengan Database

## <PERSON><PERSON><PERSON>, data yang diinsert ke database tidak sesuai dengan state checkbox yang terlihat di UI setelah user melakukan pagination atau search. Hal ini terjadi karena:

1. **Checkbox states hilang** saat pagination/search karena data table di-reload dari server
2. **Form submission hanya mengirim** checkbox yang tercentang di DOM saat submit, bukan state yang sebenarnya dari user interaction
3. **Database insertion tidak akurat** karena tidak mempertimbangkan user interactions (selected/unselected arrays)

## Solusi yang Diimplementasikan

### 1. Frontend (JavaScript) - Form Submission Enhancement

**File yang dimodifikasi:**
- `application/views/farmasi/formulir.php`
- `application/views/farmasi/editLayanan.php`

**Perubahan:**
```javascript
// Menambahkan selected/unselected arrays ke form data sebelum submit
formData.append('selectedResep', JSON.stringify(selectedResep));
formData.append('unselectedResep', JSON.stringify(unselectedResep));
formData.append('selectedPK', JSON.stringify(selectedPK));
formData.append('unselectedPK', JSON.stringify(unselectedPK));
formData.append('selectedSito', JSON.stringify(selectedSito));
formData.append('unselectedSito', JSON.stringify(unselectedSito));
formData.append('selectedHisto', JSON.stringify(selectedHisto));
formData.append('unselectedHisto', JSON.stringify(unselectedHisto));
formData.append('selectedImuno', JSON.stringify(selectedImuno));
formData.append('unselectedImuno', JSON.stringify(unselectedImuno));
```

### 2. Backend (PHP Controller) - Data Processing Logic

**File yang dimodifikasi:**
- `application/controllers/Farmasi.php`

**Method baru yang ditambahkan:**
```php
private function processFinalCheckboxState($originalData, $selectedArray, $unselectedArray)
{
    // Start with original data (items that were checked by default)
    $finalData = $originalData ?: [];
    
    // Add explicitly selected items
    foreach ($selectedArray as $item) {
        if (!in_array($item, $finalData)) {
            $finalData[] = $item;
        }
    }
    
    // Remove explicitly unselected items
    $finalData = array_filter($finalData, function($item) use ($unselectedArray) {
        return !in_array($item, $unselectedArray);
    });
    
    // Re-index array to ensure clean numeric indices
    return array_values($finalData);
}
```

**Perubahan di method `submitFormulir()`:**
```php
// Get selected/unselected arrays from form submission
$selectedResep = json_decode($this->input->post('selectedResep'), true) ?: [];
$unselectedResep = json_decode($this->input->post('unselectedResep'), true) ?: [];
// ... (untuk semua jenis checkbox)

// Process final checkbox states based on user interactions
$finalResep = $this->processFinalCheckboxState($resep, $selectedResep, $unselectedResep);
$finalPK = $this->processFinalCheckboxState($input_pk, $selectedPK, $unselectedPK);
// ... (untuk semua jenis checkbox)

// Menggunakan $finalResep, $finalPK, dll. untuk database insertion
```

## Cara Kerja Solusi

### 1. User Interaction Tracking
- Array `selectedXxx` menyimpan ID item yang explicitly dipilih user
- Array `unselectedXxx` menyimpan ID item yang explicitly di-uncheck user
- Arrays ini tetap konsisten meskipun terjadi pagination/search

### 2. Final State Processing
Saat form submission, method `processFinalCheckboxState()` menggabungkan:
- **Original data**: Checkbox yang sudah tercentang dari database
- **Selected items**: Item yang explicitly dipilih user
- **Unselected items**: Item yang explicitly di-uncheck user

### 3. Database Insertion
Data final yang sudah diproses digunakan untuk database insertion, memastikan konsistensi antara UI dan database.

## Test Cases yang Berhasil

1. ✅ **User menambah selection**: Original [1,2] + Selected [3,4] = Result [1,2,3,4]
2. ✅ **User menghapus selection**: Original [1,2,3] - Unselected [2] = Result [1,3]
3. ✅ **User menambah dan menghapus**: Original [1,2] + Selected [3,4] - Unselected [1] = Result [2,3,4]
4. ✅ **Empty original data**: Original [] + Selected [1,2] = Result [1,2]
5. ✅ **Null original data**: Original null + Selected [1,2] = Result [1,2]

## Keuntungan Solusi

1. **Konsistensi UI-Database**: Data yang diinsert sesuai dengan yang terlihat di UI
2. **Backward Compatible**: Tidak merusak fungsionalitas yang sudah ada
3. **Robust**: Menangani berbagai edge cases (null data, empty arrays, dll.)
4. **Maintainable**: Logika terpusat dalam satu method yang mudah dipahami

## File yang Tidak Perlu Diubah

- `application/views/farmasi/detailLayanan.php` - Halaman view-only tanpa form submission

## Implementasi Selesai

Solusi ini telah diimplementasikan dan siap digunakan. Checkbox states sekarang akan tetap konsisten antara UI dan database insertion, menyelesaikan masalah yang dilaporkan user.
