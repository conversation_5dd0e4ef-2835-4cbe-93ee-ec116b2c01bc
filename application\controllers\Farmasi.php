<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Farmasi extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->userdata('logged_in') == false) {
            redirect('login');
        }

        $this->load->library('form_validation');

        date_default_timezone_set('Asia/Jakarta');
        $this->load->model(
            [
                'Farmasi/FarmasiModel',
                'Farmasi/OrderDetailResepModel',
                'Farmasi/ResepModel',
                'Farmasi/OrderResepModel',
                'PAModel',
                'PKModel',
                'VerifBpjsModel',
                'Farmasi/RadiologiModel'
            ]
        );
        ini_set('display_errors', 1);
    }
    public function index()
    {
        echo 'Versi PHP Anda adalah: ' . phpversion();
    }

    public function indexFarmasi()
    {
        $data = [
            'title' => 'Data Farmasi | VBPJS RSKD',
            'header' => 'Data Farmasi',
            'isi' => 'farmasi/index',
            'session' => $this->session->get_userdata(),
        ];
        // echo '<pre>';print_r($data);exit();

        $this->load->view('layout/wrapper', $data);
    }

    public function tabel()
    {
        $post = $this->input->post();
        // echo '<pre>';print_r($post);exit();
        $mulai = isset($post['mulai']) ? $post['mulai'] : null;
        $akhir = isset($post['akhir']) ? $post['akhir'] : null;
        $no_mr = isset($post['mr']) ? $post['mr'] : null;
        $no_bpjs = isset($post['bpjs']) ? $post['bpjs'] : null;

        $list = $this->FarmasiModel->ambilTabel($mulai, $akhir, $no_mr, $no_bpjs);
        $no = isset($_POST['start']) ? intval($_POST['start']) : 0;
        $data = [];
        $session = $this->session->get_userdata();
        $hapus = null;

        foreach ($list as $l) {
            $jenis = '';
            if ($l->jenis_data == 1) {
                $jenis = 'Hasil penunjang eksternal dan internal';
            } elseif ($l->jenis_data == 2) {
                $jenis = 'JPOK';
            } elseif ($l->jenis_data == 3) {
                $jenis = '<em>Billing</em> layanan';
            }

            if (isset($session['type']) && $session['type'] != 'BPJS') {
                $hapus = "<button href='#modal-hapus-farmasi' class='btn btn-danger' id='tbl-hapus-farmasi' data-toggle='modal' data-id='" . htmlspecialchars($l->id) . "'>Hapus</button>";
            }

            $row = [];
            $row[] = ++$no . '.';
            $row[] = htmlspecialchars($l->norm);
            $row[] = htmlspecialchars($l->nama);
            $row[] = htmlspecialchars($l->nobpjs);
            $row[] = date('d/m/Y', strtotime($l->tgllayanan));
            $row[] = $jenis;
            $row[] = "<div class='btn-group btn-block' role='group'>
                    <a href='" . base_url("uploads/" . urlencode($l->file)) . "' class='btn btn-default' target='_blank'>Lihat</a>" . $hapus . "
                  </div>";
            $data[] = $row;
        }

        $output = [
            'draw' => isset($_POST['draw']) ? intval($_POST['draw']) : 0,
            'recordsTotal' => $this->FarmasiModel->hitungSemua($mulai, $akhir, $no_mr, $no_bpjs),
            'recordsFiltered' => $this->FarmasiModel->hitungTersaring($mulai, $akhir, $no_mr, $no_bpjs),
            'data' => $data,
        ];

        echo json_encode($output);
    }

    public function tableOldData()
    {
        $post       = $this->input->post();
        $mulai      = isset($post['mulai']) ? $post['mulai'] : null;
        $akhir      = isset($post['akhir']) ? $post['akhir'] : null;
        $no_mr      = isset($post['nomorMr']) ? $post['nomorMr'] : null;
        $no_bpjs    = isset($post['nomorBpjs']) ? $post['nomorBpjs'] : null;
        $view_only  = isset($post['viewOnly']) ? $post['viewOnly'] : null;

        $list = $this->FarmasiModel->ambilTabel($mulai, $akhir, $no_mr, $no_bpjs, $view_only);
        $filtered = $this->FarmasiModel->hitungTersaring($mulai, $akhir, $no_mr, $no_bpjs, $view_only);
        $total = $this->FarmasiModel->hitungSemua($mulai, $akhir, $no_mr, $no_bpjs, $view_only);

        $data = [];
        $no = isset($_POST['start']) ? intval($_POST['start']) : 0;

        foreach ($list as $l) {
            $jenis = '';
            if ($l->jenis_data == 1) {
                $jenis = 'Hasil penunjang eksternal dan internal';
            } elseif ($l->jenis_data == 2) {
                $jenis = 'JPOK';
            } elseif ($l->jenis_data == 3) {
                $jenis = '<em>Billing</em> layanan';
            }

            // Inisialisasi checkbox hanya jika view_only tidak null
            $checkbox = '';
            if ($view_only == null) {
                $checked = ''; // Tambahkan logika untuk kondisi checked jika perlu
                if ($l->id == $l->id_uploads_bpjs) {
                    $checked = 'checked';
                }
                $checkbox = '<input type="checkbox" name="input_old_table[]" class="styled-checkbox inpOldTable" value="' . $l->id . '" data-id="' . $l->id . '" ' . $checked . ' />';
            }

            $row = [];
            $row[] = ++$no . '.';
            $row[] = date('d/m/Y', strtotime($l->tgllayanan));
            $row[] = $jenis;

            $row[] = "<div class='btn-group btn-block' role='group'>
                        <a href='" . base_url("uploads/" . urlencode($l->file)) . "' class='btn btn-default' target='_blank'>Lihat</a></div>";
            // Tambahkan checkbox di sini jika tersedia
            if ($view_only == null) {
                $row[] = $checkbox;
            }
            $data[] = $row;
        }

        $output = [
            "draw" => $_POST['draw'],
            "recordsTotal" => $total,
            "recordsFiltered" => $filtered,
            "data" => $data
        ];

        echo json_encode($output);
    }


    public function inputFarmasi()
    {
        $session = $this->session->get_userdata();
        if ($session['type'] == 'BPJS') {
            redirect('error404');
        } else {
            $data = [
                'title' => 'Input Data Farmasi | VBPJS RSKD',
                'header' => 'Input Data Farmasi',
                'isi' => 'farmasi/input',
                'session' => $session,
                'csrf_token' => $this->security->get_csrf_token_name(),
                'csrf_hash' => $this->security->get_csrf_hash(),
            ];
            // echo '<pre>';print_r($data);exit();

            $this->load->view('layout/wrapper', $data);
        }
    }

    public function simpan($param)
    {
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            $this->db->trans_begin();
            $post = $this->input->post();
            // echo '<pre>';print_r($post);exit();

            if ($param == 'hasil') {
                // Simpan hasil
                $namaFile = rand(100, 100000) . '-' . str_replace(
                    ' ',
                    '_',
                    str_replace(
                        "'",
                        '~',
                        $_FILES['hasil']['name']
                    )
                );
                $config['upload_path'] = './uploads/';
                $config['allowed_types'] = 'pdf|jpg|jpeg|png';
                $config['file_name'] = $namaFile;
                $config['max_size'] = 10000;
                $this->load->library('upload', $config);
                if ($this->upload->do_upload('hasil')) {
                    $post = [
                        'file' => $namaFile,
                        'type' => $_FILES['hasil']['type'],
                        'size' => $_FILES['hasil']['size'] / 1024,
                        'tgllayanan' => $post['tgllayanan'],
                        'tanggal' => date('Y-m-d H:i:s'),
                        'norm' => $post['norm'],
                        'jenis_data' => 1,
                        'nobpjs' => $post['nobpjs'],
                        'oleh' => $this->session->userdata['id'],
                    ];
                    // echo '<pre>';print_r($post);exit();
                    $this->FarmasiModel->simpan($post);
                }
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            } elseif ($param == 'jpok') {
                // Simpan JPOK
                $namaFile = rand(100, 100000) . '-' . str_replace(
                    ' ',
                    '_',
                    str_replace(
                        "'",
                        '~',
                        $_FILES['jpok']['name']
                    )
                );
                $config['upload_path'] = './uploads/';
                $config['allowed_types'] = 'pdf|jpg|jpeg|png';
                $config['file_name'] = $namaFile;
                $config['max_size'] = 10000;
                $this->load->library('upload', $config);
                if ($this->upload->do_upload('jpok')) {
                    $post = [
                        'file' => $namaFile,
                        'type' => $_FILES['jpok']['type'],
                        'size' => $_FILES['jpok']['size'] / 1024,
                        'tgllayanan' => $post['tgllayanan'],
                        'tanggal' => date('Y-m-d H:i:s'),
                        'norm' => $post['norm'],
                        'jenis_data' => 2,
                        'nobpjs' => $post['nobpjs'],
                        'oleh' => $this->session->userdata['id'],
                    ];
                    // echo '<pre>';print_r($post);exit();
                    $this->FarmasiModel->simpan($post);
                }
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            } elseif ($param == 'billing') {
                // Simpan billing
                $namaFile = rand(100, 100000) . '-' . str_replace(
                    ' ',
                    '_',
                    str_replace(
                        "'",
                        '~',
                        $_FILES['billing']['name']
                    )
                );
                $config['upload_path'] = './uploads/';
                $config['allowed_types'] = 'pdf|jpg|jpeg|png';
                $config['file_name'] = $namaFile;
                $config['max_size'] = 10000;
                $this->load->library('upload', $config);
                if ($this->upload->do_upload('billing')) {
                    $post = [
                        'file' => $namaFile,
                        'type' => $_FILES['billing']['type'],
                        'size' => $_FILES['billing']['size'] / 1024,
                        'tgllayanan' => $post['tgllayanan'],
                        'tanggal' => date('Y-m-d H:i:s'),
                        'norm' => $post['norm'],
                        'jenis_data' => 3,
                        'nobpjs' => $post['nobpjs'],
                        'oleh' => $this->session->userdata['id'],
                    ];
                    // echo '<pre>';print_r($post);exit();
                    $this->FarmasiModel->simpan($post);
                }
                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            }
        }
    }

    public function hapus()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($session['type'] == 'BPJS') {
                redirect('error404');
            } else {
                $this->db->trans_begin();
                $post = $this->input->post();
                $id = $post['id'] ? $post['id'] : null;
                $data = ['status' => 0];

                // echo '<pre>';print_r($data);exit();
                $this->FarmasiModel->ubah($id, $data);

                if ($this->db->trans_status() === false) {
                    $this->db->trans_rollback();
                    $result = ['status' => 'failed'];
                } else {
                    $this->db->trans_commit();
                    $result = ['status' => 'success'];
                }
                echo json_encode($result);
            }
        }
    }

    public function tabelResep()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($session['type'] == 'BPJS') {
                redirect('error404');
            } else {
                $data = [];
                $post = $this->input->post();
                // echo '<pre>';print_r($post);exit();
                $norm = $post['norm'];
                $draw = intval($post['draw']);
                $no = $_POST['start'];
                $tabel = $this->OrderResepModel->ambilTabel($norm);
                // echo '<pre>';print_r($tabel);exit();

                foreach ($tabel as $t) {
                    $data[] = [
                        ++$no . '.',
                        $t->ASAL_UNIT,
                        $t->TUJUAN,
                        $t->OLEH,
                        date('d/m/Y, H.i.s', strtotime($t->TANGGAL_RESEP)),
                        $t->STATUS_RESEP,
                        "<a href='#modal-resep-farmasi' class='btn btn-sm btn-block btn-primary tbl-resep-farmasi' data-toggle='modal' data-id='" . $t->NOMOR_ORDER . "' data-status='" . $t->STATUS . "'>
                            <i class='fa fa-eye'></i> Lihat
                        </a>",
                    ];
                }

                $output = [
                    'draw' => $draw,
                    'recordsTotal' => $this->OrderResepModel->hitungSemua($norm),
                    'recordsFiltered' => $this->OrderResepModel->hitungTersaring($norm),
                    'data' => $data,
                ];
                echo json_encode($output);
            }
        }
    }
    public function tabelResepBaru()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            try {
                $data = [];
                $post = $this->input->post();
                $norm = $post['norm'];
                $draw = intval($post['draw']);
                $no = $_POST['start'];
                $view_only = null !== $this->input->post('view_only') ? $this->input->post('view_only') : null;
                $id_layanan = null !== $this->input->post('id_layanan') ? $this->input->post('id_layanan') : null;

                $tabel = $this->ResepModel->ambilTabel($norm, $id_layanan, $view_only);

                if (!is_array($tabel)) {
                    throw new Exception('Gagal mengambil data tabel');
                }

                $checkedData = [];
                $uncheckedData = [];

                foreach ($tabel as $t) {
                    $checkbox = null;
                    $checked = isset($t->ID_RESEP) && $t->NOMOR_ORDER == $t->ID_RESEP ? 'checked' : '';

                    if ($view_only != 1) {
                        $checkbox = '<input type="checkbox" name="input_resep[]" class="styled-checkbox pilihResep" value="' . $t->NOMOR_ORDER . '" data-id="' . $t->NOMOR_ORDER . '" ' . $checked . ' />';
                    }

                    $row = [
                        ++$no . '.',
                        $t->ASAL_UNIT,
                        $t->TUJUAN,
                        $t->sep_resep,
                        $t->OLEH,
                        date('d/m/Y, H.i.s', strtotime($t->TANGGAL_RESEP)),
                        $t->STATUS_RESEP,
                        "<a href='#modal-resep-farmasi' class='btn btn-sm btn-block btn-primary tbl-resep-farmasi' data-toggle='modal' data-id='" . $t->NOMOR_ORDER . "' data-status='" . $t->STATUS . "'>
                        <i class='fa fa-eye'></i> Lihat
                    </a>
                    <a class='btn btn-sm btn-block btn-secondary cetakResep' data-id='" . $t->NOKUN . "'>
                        <i class='fa fa-print'></i> Cetak
                    </a>",
                        $checkbox
                    ];

                    // Store checked and unchecked rows separately
                    if ($checked) {
                        $checkedData[] = $row; // Store checked rows
                    } else {
                        $uncheckedData[] = $row; // Store unchecked rows
                    }
                }

                // Merge checked and unchecked data, with checked first
                $data = array_merge($checkedData, $uncheckedData);

                $output = [
                    'draw' => $draw,
                    'recordsTotal' => $this->ResepModel->hitungSemua($norm, $id_layanan, $view_only),
                    'recordsFiltered' => $this->ResepModel->hitungTersaring($norm, $id_layanan, $view_only),
                    'data' => $data,
                ];

                echo json_encode($output);
            } catch (Exception $e) {
                echo json_encode([
                    'draw' => 0,
                    'recordsTotal' => 0,
                    'recordsFiltered' => 0,
                    'data' => [],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    public function lihatResep()
    {
        $nomor = $this->input->post('nomor');
        $data = [
            'detail' => $this->OrderResepModel->detail($nomor),
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
        ];
        // echo '<pre>';print_r($data);exit();
        $this->load->view('resep/modal', $data);
    }

    public function tabelDetailResep()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

            $generik = null;
            $kardek = null;
            $ambilJalur = null;
            $jalur = null;
            $data = [];
            $post = $this->input->post();
            // echo '<pre>';print_r($post);exit();
            $id = $post['id'];
            $draw = intval($post['draw']);
            $no = $_POST['start'];
            $tabel = $this->OrderDetailResepModel->ambilTabel($id);
            // echo '<pre>';print_r($tabel);exit();

            foreach ($tabel as $t) {
                // Mulai generik
                if (isset($t->GENERIK)) {
                    $generik = $t->GENERIK;
                } else {
                    $generik = null;
                }
                // Akhir generik

                // Mulai kardek
                if ($t->KARDEX == 1) {
                    $kardek = 'Ya';
                } else {
                    $kardek = 'Tidak';
                }
                // Akhir kardek

                // Mulai jalur
                if ($t->JALUR_PEMBERIAN != null) {
                    $ambilJalur = $this->OrderDetailResepModel->ambilJalurPemberian($t->JALUR_PEMBERIAN);
                    $jalur = $ambilJalur ? $ambilJalur : '-';
                } else {
                    $jalur = '-';
                }
                // Akhir jalur

                $data[] = [
                    ++$no . '.',
                    $t->KLP_RACIKAN,
                    $t->NAMA_OBAT . ' ' . $generik,
                    $t->JUMLAH,
                    $t->ATURAN_PAKAI,
                    $t->KETERANGAN ? $t->KETERANGAN : '-',
                    $kardek,
                    $jalur
                ];
            }

            $output = [
                'draw' => $draw,
                'recordsTotal' => $this->OrderDetailResepModel->hitungSemua($id),
                'recordsFiltered' => $this->OrderDetailResepModel->hitungTersaring($id),
                'data' => $data,
            ];
            echo json_encode($output);
        }
    }

    public function data()
    {

        $data = [
            'title' => 'Data Farmasi | VBPJS RSKD',
            'header' => 'Data Farmasi',
            'isi' => 'farmasi/index_new',
            'page' => 'farmasi-new',
            'session' => $this->session->get_userdata(),
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
        ];
        // echo '<pre>';print_r($data);exit();

        $this->load->view('layout/wrapper', $data);
    }

    public function cariMRFarmasi()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($session['type'] == 'BPJS') {
                redirect('error404');
            } else {

                $norm = $this->input->post('norm');

                if (empty($norm)) {
                    echo json_encode(['error' => 'Nomor Rekam Medis tidak boleh kosong']);
                    return;
                }

                $data = $this->FarmasiModel->getNomr($norm);

                if ($data) {
                    echo json_encode($data);
                } else {
                    echo json_encode(['error' => 'Nomor Rekam Medis tidak ditemukan']);
                }
            }
        }
    }

    public function cariBPJSFarmasi()
    {
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            if ($session['type'] == 'BPJS') {
                redirect('error404');
            } else {

                $no_bpjs = $this->input->post('no_bpjs');

                if (empty($no_bpjs)) {
                    echo json_encode(['error' => 'Nomor BPJS tidak boleh kosong']);
                    return;
                }

                $this->load->model('PasienModel');
                $data = $this->PasienModel->ambilPasienBPJS($no_bpjs);

                if ($data) {
                    echo json_encode($data);
                } else {
                    echo json_encode(['error' => 'Nomor BPJS tidak ditemukan']);
                }
            }
        }
    }

    public function cariVerifBpjs()
    {
        if ($this->input->is_ajax_request()) {
            $norm = $this->input->post('norm');
            $akses = $this->session->userdata('type');

            if (empty($norm)) {
                echo json_encode(['error' => 'Nomor Rekam Medis / BPJS tidak boleh kosong']);
                return;
            }

            $data = $this->VerifBpjsModel->get_data_verif($norm);
            $result = [];

            if ($data) {
                foreach ($data as $index => $item) {
                    // Buat tombol aksi berdasarkan akses
                    $actions = '<a class="btn btn-info btn-sm" onclick="lihatDetail(' . htmlspecialchars($item['norm']) . ', ' . htmlspecialchars($item['id_layanan']) . ')">
                                    <i class="fa fa-eye"></i> Lihat
                                </a>';

                    if ($akses != 'BPJS') {
                        $actions .= ' <a class="btn btn-danger btn-sm deleteBtn" data-id="' . htmlspecialchars($item['id_layanan']) . '">
                                        <i class="fa fa-trash"></i> Hapus
                                      </a>';
                    }

                    $result[] = [
                        'index' => $index + 1,
                        'tanggal_pelayanan' => htmlspecialchars($item['tanggal_pelayanan']),
                        'nama_pasien' => htmlspecialchars($item['nama_pasien']),
                        'no_bpjs' => htmlspecialchars($item['no_bpjs']),
                        'created_at' => htmlspecialchars($item['created_at']),
                        'actions' => $actions
                    ];
                }
            }

            echo json_encode([
                "draw" => intval($this->input->post('draw')),
                "akses" => $akses,
                "recordsTotal" => count($data),
                "recordsFiltered" => count($data),
                "data" => $result
            ]);
        }
    }





    public function formData()
    {

        $norm = $this->input->get('norm');
        $data_pasien = $this->FarmasiModel->get_data_pasien($norm);
        $verif_exist = $this->VerifBpjsModel->get_verifikasi_awal($norm);
        $file_exist = $this->VerifBpjsModel->get_verif_files(false, $norm);
        $jpok_exist = null;
        foreach ($file_exist as $file) {
            if ($file->file_category == 1) {
                $jpok_exist = $file; // or assign specific property if needed, e.g., $file->file_name
                break; // Stop looping once we find the first match
            }
        }
        if (!$norm || !$data_pasien) {
            redirect('Farmasi/data');
        }
        $data = [
            'title' => 'Form Input | VBPJS RSKD',
            'header' => 'Form Input ',
            'isi' => 'farmasi/formulir',
            'page' => 'farmasi-new',
            'session' => $this->session->get_userdata(),
            'verif_exist' => $verif_exist,
            'file_exist' => $file_exist,
            'jpok_exist' => $jpok_exist,
            'norm' => $norm,
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
            'data_pasien' => $data_pasien
        ];
        // echo '<pre>';print_r($data);exit();

        $this->load->view('layout/wrapper', $data);
    }

    public function editLayanan()
    {
        $norm = $this->input->get('norm');
        $id_layanan = $this->input->get('id_layanan');
        $data_verified = $this->VerifBpjsModel->get_data_verif($norm, $id_layanan);
        $data_pasien = $this->FarmasiModel->get_data_pasien($norm);
        $verif_exist = $this->VerifBpjsModel->get_verifikasi_awal($norm);
        $file_exist = $this->VerifBpjsModel->get_verif_files(false, $norm);

        $jpok_exist = null; // Initialize jpok_exist to null
        if (!empty($file_exist)) {
            foreach ($file_exist as $file) {
                if (isset($file->file_category) && $file->file_category == 1) {
                    $jpok_exist = $file; // or assign specific property if needed, e.g., $file->file_name
                    break; // Stop looping once we find the first match
                }
            }
        }

        if (!$norm || empty($data_pasien) || empty($data_verified)) {
            redirect('Farmasi/data');
        }

        $data = [
            'title' => 'Form Edit | VBPJS RSKD',
            'header' => 'Form Edit ',
            'isi' => 'farmasi/editLayanan',
            'page' => 'farmasi-new',
            'session' => $this->session->get_userdata(),
            'verif_exist' => isset($verif_exist) ? $verif_exist : null,
            'jpok_exist' => $jpok_exist,
            'file_exist' => isset($file_exist) ? $file_exist : [],
            'norm' => $norm,
            'id_layanan' => $id_layanan,
            'data_verified' => isset($data_verified) ? $data_verified : null,
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
            'data_pasien' => isset($data_pasien) ? $data_pasien : null
        ];

        $this->load->view('layout/wrapper', $data);
    }

    public function detailLayanan()
    {
        $id_layanan = $this->input->get('id_layanan');
        $norm = $this->input->get('norm');
        $data_pasien = $this->FarmasiModel->get_data_pasien($norm);
        $data_verified = $this->VerifBpjsModel->get_data_verif($norm, $id_layanan);
        $query = $this->db->last_query();
        // print_r($query);
        // exit();

        if (!$norm || empty($data_pasien) || empty($data_verified)) {
            redirect('Farmasi/data');
        }

        $data = [
            'title' => 'Detail Data Layanan | VBPJS RSKD',
            'header' => 'Detail Data Layanan',
            'isi' => 'farmasi/detailLayanan',
            'page' => 'farmasi',
            'session' => $this->session->get_userdata(),
            'norm' => $norm,
            'id_layanan' => $id_layanan,
            'data_verified' => isset($data_verified) ? $data_verified : null,
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
            'data_pasien' => isset($data_pasien) ? $data_pasien : null
        ];

        $this->load->view('layout/wrapper', $data);
    }


    public function get_data_sito()
    {
        $draw = intval($this->input->POST("draw"));
        $start = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
        $view_only = $this->input->post('view_only') ?: null;

        $mr = $this->input->post('mr', true);
        $listSito = $this->PAModel->datatablesSito($mr, $view_only);

        $data = [];
        $no = $_POST['start'];
        foreach ($listSito as $LS) {
            $no++;

            // Add a checkbox input next to the 'Cetak' button
            $button = '<a class="btn btn-primary cetakSito" href="javascript:;" data="' . $LS->ID . '" nolab="' . $LS->NOMOR_LAB . '"><i class="fa fa-print"></i> Cetak</a>';

            // Add the checkbox after the button with a class for styling
            if ($view_only != 1) {
                $checkbox = '<input type="checkbox" name="input_sito[]" class="cekSito styled-checkbox" value="' . $LS->ID . '" data-id="' . $LS->ID . '" ' . ($LS->ID == $LS->ID_SITO ? 'checked' : '') . ' />';
            }

            $data[] = [
                $no,
                $LS->NOMOR_LAB,
                $LS->TANGGAL_ORDER,
                $button,
                $checkbox  // Move checkbox after the button
            ];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->PAModel->total_count_Sito($mr, $view_only),
            'recordsFiltered' => $this->PAModel->filter_count_Sito($mr, $view_only),
            'data' => $data
        ];
        echo json_encode($output);
    }


    public function get_data_histo()
    {
        $draw = intval($this->input->POST("draw"));
        $start = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
        $view_only = $this->input->post('view_only') ?: null;
        $mr = $this->input->post('mr', true);
        $listHisto = $this->PAModel->datatablesHisto($mr, $view_only);


        $data = [];
        $no = $_POST['start'];
        foreach ($listHisto as $LH) {
            $no++;

            $button = '<a class="btn btn-primary cetakHisto" href="javascript:;" data="' . $LH->ID . '" nolab="' . $LH->NOMOR_LAB . '"><i class="fa fa-print"></i> Cetak</a>';
            // Add the checkbox after the button with a class for styling
            if ($view_only != 1) {
                $checkbox = '<input type="checkbox" name="input_histo[]" class="cekHisto styled-checkbox" value="' . $LH->ID . '" data-id="' . $LH->ID . '" ' . ($LH->ID == $LH->ID_HISTO ? 'checked' : '') . ' />';
            }
            $data[] = [
                $no,
                $LH->NOMOR_LAB,
                $LH->TANGGAL_ORDER,
                $button,
                $checkbox
            ];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->PAModel->total_count_Histo($mr, $view_only),
            'recordsFiltered' => $this->PAModel->filter_count_Histo($mr, $view_only),
            'data' => $data
        ];
        echo json_encode($output);
    }

    public function get_data_imuno()
    {
        $draw = intval($this->input->POST("draw"));
        $start = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
        $view_only = $this->input->post('view_only') ?: null;

        $mr = $this->input->post('mr', true);
        $listImuno = $this->PAModel->datatablesImuno($mr, $view_only);
        $checkbox = '';

        $data = [];
        $no = $_POST['start'];
        foreach ($listImuno as $LI) {
            $no++;

            $button = '<a class="btn btn-primary cetakImuno" href="javascript:;" data="' . $LI->ID . '" nolab="' . $LI->NOMOR_IMUNO . '"><i class="fa fa-print"></i> Cetak</a>';
            // Add the checkbox after the button with a class for styling
            if ($view_only != 1) {
                $checkbox = '<input type="checkbox" name="input_imuno[]" class="cekImuno styled-checkbox" value="' . $LI->ID . '" data-id="' . $LI->ID . '" ' . ($LI->ID == $LI->ID_IMUNO ? 'checked' : '') . ' />';
            }
            $data[] = [
                $no,
                $LI->NOMOR_IMUNO,
                $LI->TANGGAL_ORDER,
                $button,
                $checkbox
            ];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->PAModel->total_count_Imuno($mr, $view_only),
            'recordsFiltered' => $this->PAModel->filter_count_Imuno($mr, $view_only),
            'data' => $data
        ];
        echo json_encode($output);
    }

    public function get_data_mole()
    {
        $draw = intval($this->input->POST("draw"));
        $start = intval($this->input->POST("start"));
        $length = intval($this->input->POST("length"));
        $view_only = $this->input->post('view_only') ?: null;

        $mr = $this->input->post('mr', true);
        $listPatmol = $this->PAModel->datatablesPatmol($mr, $view_only);

        $data = [];
        $no = $_POST['start'];
        foreach ($listPatmol as $LP) {
            $no++;

            $button = '<a class="btn btn-primary cetakMole" href="javascript:;" data="' . $LP->NOMOR_PATMOL . '" jenis="' . $LP->JENIS_PEMERIKSAAN . '" desk="' . $LP->DESK_JENIS_PEMERIKSAAN . '"><i class="fa fa-print"></i> Cetak</a>';
            // Add the checkbox after the button with a class for styling
            if ($view_only != 1) {
                $checkbox = '<input type="checkbox" name="input_mole[]" class="cekMole styled-checkbox" value="' . $LP->ID . '" data-id="' . $LP->ID . '" ' . ($LP->ID == $LP->ID_MOLE ? 'checked' : '') . ' />';
            }
            $data[] = [
                $no,
                $LP->NOMOR_PATMOL . ' (' . $LP->DESK_JENIS_PEMERIKSAAN . ')',
                $LP->TANGGAL_LAB,
                $button,
                $checkbox
            ];
        }

        $output = [
            'draw' => $draw,
            'recordsTotal' => $this->PAModel->total_count_Patmol($mr, $view_only),
            'recordsFiltered' => $this->PAModel->filter_count_Patmol($mr, $view_only),
            'data' => $data
        ];
        echo json_encode($output);
    }

    public function tableRadiologi()
    {
        $tanggalTindakan = null;
        $session = $this->session->get_userdata();
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

            $data = [];
            $post = $this->input->post();
            $norm = $post['norm'];
            $part = null !== $this->input->post('part') ? $this->input->post('part') : null;
            $view_only = null !== $this->input->post('view_only') ? $this->input->post('view_only') : null;
            $id_layanan = null !== $this->input->post('id_layanan') ? $this->input->post('id_layanan') : null;
            $name = 'input_radiologi' . $part . '[]';
            $draw = intval($post['draw']);

            $tabel = $this->RadiologiModel->ambilTabel($norm, $view_only, $part, $id_layanan);

            foreach ($tabel as $t) {
                // Start checking the date
                if (isset($t->TANGGALTINDAKAN) && !empty($t->TANGGALTINDAKAN)) {
                    $tanggalTindakan = date('d/m/Y', strtotime($t->TANGGALTINDAKAN));
                } else {
                    $tanggalTindakan = null; // Set to null instead of '-'
                }

                if ($part == 1) {
                    // $selected_idtm = $this->VerifBpjsModel->get_verif_radiologi($t->IDTM, $norm, $id_layanan);
                } else {
                    $selected_idtm = null;
                }

                $checked = (isset($t->ID_TINMED) && $t->IDTM == $t->ID_TINMED) ? 'checked' : '';

                $checkbox = "<div class='form-check'>
                                <input type='checkbox' name='" . $name . "' class='form-check-input styled-checkbox checkRadiologi' value='" . $t->IDTM . "' aria-label='" . (isset($t->TANGGALTINDAKAN) ? $t->TANGGALTINDAKAN : '') . "' $checked>
                             </div>";

                $expertise_print = "<div class='row'>
                                    <div class='col-md-6'>
                                        <a href='#modal-expertise-farmasi' data-toggle='modal' data-tindakan='" . $t->IDTM . "' class='btn btn-sm btn-success btn-block tbl-expertise-farmasi'>
                                            <i class='fa fa-file-text'></i> <em>Expertise</em>
                                        </a>
                                    </div>
                                    <div class='col-md-6'>
                                        <a class='btn btn-sm btn-info btn-block btn-cetak' data-id='" . $t->IDTM . "'>
                                            <i class='fa fa-print'></i> <em>Cetak</em>
                                        </a>
                                    </div>
                                </div>";

                // Only include non-empty values in the data array
                $row = [
                    'checkbox' => $checkbox,
                    'tanggal' => $tanggalTindakan, // This will be null if empty
                    'nmTindakan' => !empty($t->NMTINDAKAN) ? $t->NMTINDAKAN : null, // Set to null if empty
                    'expertise' => $expertise_print,
                    'checked' => !empty($checked), // Store whether the item is checked
                ];

                // Add the row to the data array
                $data[] = $row;
            }

            // Sort the data so checked items come first
            usort($data, function ($a, $b) {
                return $a['checked'] < $b['checked'] ? 1 : ($a['checked'] > $b['checked'] ? -1 : 0);
            });


            // Prepare the output
            if ($view_only == 1) {
                $output = [
                    'draw' => $draw,
                    'recordsTotal' => $this->RadiologiModel->hitungSemua($norm, $view_only, $part, $id_layanan),
                    'recordsFiltered' => $this->RadiologiModel->hitungTersaring($norm, $view_only, $part, $id_layanan),
                    'data' => array_map(function ($item) {
                        return [
                            $item['tanggal'],
                            $item['nmTindakan'],
                            $item['expertise'],
                        ];
                    }, $data),
                ];
            } else {
                $output = [
                    'draw' => $draw,
                    'recordsTotal' => $this->RadiologiModel->hitungSemua($norm, $view_only, $part, $id_layanan),
                    'recordsFiltered' => $this->RadiologiModel->hitungTersaring($norm, $view_only, $part, $id_layanan),
                    'data' => array_map(function ($item) {
                        return [
                            $item['checkbox'],
                            $item['tanggal'],
                            $item['nmTindakan'],
                            $item['expertise'],
                        ];
                    }, $data),
                ];
            }

            echo json_encode($output);
        }
    }


    public function tablePK()
    {
        $norm = $this->input->post('norm');
        $limit = $this->input->post('length');  // Number of records to fetch
        $start = $this->input->post('start');   // Starting record
        $searchValue = $this->input->post('search')['value'];  // Search term from DataTable

        // Use the ternary operator for null safety
        $id_layanan = null !== $this->input->post('id_layanan') ? $this->input->post('id_layanan') : null;
        $view_only = null !== $this->input->post('view_only') ? $this->input->post('view_only') : null;

        // Get the total count of records for this query without limit (and without search)
        $totalData = $this->PKModel->count_kunjungan_pk($norm, $id_layanan, $view_only);

        // Get the total count of filtered records (with search applied)
        $totalFiltered = $this->PKModel->count_filtered_kunjungan_pk($norm, $searchValue, $id_layanan, $view_only);

        // Fetch the records with pagination and search
        $data_pk = $this->PKModel->get_kunjungan_pk($norm, $limit, $start, $searchValue, $id_layanan, $view_only);

        $data = [];
        foreach ($data_pk as $row) {
            // Check if id_layanan is null to determine if checkbox should be displayed
            $checkbox = '';
            if ($view_only != 1) {
                // Check if nokun equals id_kunjungan to determine if checkbox should be checked
                $checked = (isset($row['id_kunjungan']) && $row['nokun'] == $row['id_kunjungan']) ? 'checked' : '';
                $checkbox = '<input type="checkbox" name="input_pk[]" class="select-checkbox styled-checkbox pilihPK" value="' . $row['nokun'] . '" data-id="' . $row['nokun'] . '" ' . $checked . '>';
            }

            $action_button = '<a class="btn btn-info btn-sm viewPk" data-id="' . $row['nokun'] . '"><i class="fa fa-eye"></i> Lihat</a>';

            $data[] = [
                'pilih' => $checkbox,
                'masuk' => $row['TGLMASUK'],
                'tindakan' => $row['TINDAKAN'],
                'nokun' => $row['nokun'],
                'aksi' => $action_button
            ];
        }

        $output = [
            'draw' => intval($this->input->post('draw')),
            'recordsTotal' => $totalData,        // Total records without filtering
            'recordsFiltered' => $totalFiltered, // Total records with filtering
            'data' => $data
        ];

        echo json_encode($output);
    }



    public function view_pk()
    {
        $nokun = $this->input->post('nokun');
        $dp = $this->PKModel->data_pasien($nokun);
        $get_tgl_sampling = $this->PKModel->get_tgl_sampling($nokun);
        $get_tgl_hasil = $this->PKModel->get_tgl_hasil($nokun);
        $hasil_lab = $this->PKModel->hasil_lab($nokun);
        // $list  = $this->pk_model->list($nokun);
        // echo "<pre>";print_r($hasil_lab);exit();

        $data = [
            'dp' => $dp,
            'get_tgl_sampling' => $get_tgl_sampling,
            'get_tgl_hasil' => $get_tgl_hasil,
            'hasil_lab' => $hasil_lab,
        ];
        $this->load->view('hasil_lab_pk', $data);
    }

    public function submitFormulir()
    {
        $this->load->library('upload');

        $this->db->trans_start();
        $id_verifikasi = $this->uniqid_int();
        $id_layanan_exist = null !== $this->input->post('id_layanan') ? $this->input->post('id_layanan') : null;

        if ($id_layanan_exist == null) {
            $id_layanan = $this->uniqid_int();
        } else {
            $id_layanan = $id_layanan_exist;
        }
        // Set form validation rules
        $this->form_validation->set_rules('norm', 'No RM', 'required');
        $this->form_validation->set_rules('nobpjs', 'No BPJS', 'required');
        $this->form_validation->set_rules('nama', 'Nama Lengkap', 'required');
        $this->form_validation->set_rules('tgllayanan', 'Tanggal Layanan', 'required');
        $this->form_validation->set_rules('tglJatuhTempo', 'Tanggal Jatuh Tempo', 'required');

        if ($this->form_validation->run() === false) {
            $response = [
                'status' => 'error',
                'message' => validation_errors()
            ];
            echo json_encode($response);
            return;
        }

        // Collect main form data
        $norm = $this->input->post('norm');
        $no_bpjs = $this->input->post('nobpjs');
        $nama_lengkap = $this->input->post('nama');
        $tgl_layanan = $this->input->post('tgllayanan');
        $tgl_jatuh_tempo = $this->input->post('tglJatuhTempo');
        $post = $this->input->post();

        // Handle optional inputs
        $inputOldData = $this->input->post('input_old_table');
        $resep = $this->input->post('input_resep');
        $input_pk = $this->input->post('input_pk');
        $sitologi = $this->input->post('input_sito');
        $histologi = $this->input->post('input_histo');
        $imunohistokimia = $this->input->post('input_imuno');
        $molekuler = $this->input->post('input_molekuler');
        $radiologi_verif = $this->input->post('input_radiologi1');
        $radiologi_layanan = $this->input->post('input_radiologi2');
        $user_exist = $this->input->post('user_exist');

        // Get selected/unselected arrays from form submission
        $selectedResep = json_decode($this->input->post('selectedResep'), true) ?: [];
        $unselectedResep = json_decode($this->input->post('unselectedResep'), true) ?: [];
        $selectedPK = json_decode($this->input->post('selectedPK'), true) ?: [];
        $unselectedPK = json_decode($this->input->post('unselectedPK'), true) ?: [];
        $selectedSito = json_decode($this->input->post('selectedSito'), true) ?: [];
        $unselectedSito = json_decode($this->input->post('unselectedSito'), true) ?: [];
        $selectedHisto = json_decode($this->input->post('selectedHisto'), true) ?: [];
        $unselectedHisto = json_decode($this->input->post('unselectedHisto'), true) ?: [];
        $selectedImuno = json_decode($this->input->post('selectedImuno'), true) ?: [];
        $unselectedImuno = json_decode($this->input->post('unselectedImuno'), true) ?: [];

        // Process final checkbox states based on user interactions
        $finalResep = $this->processFinalCheckboxState($resep, $selectedResep, $unselectedResep);
        $finalPK = $this->processFinalCheckboxState($input_pk, $selectedPK, $unselectedPK);
        $finalSitologi = $this->processFinalCheckboxState($sitologi, $selectedSito, $unselectedSito);
        $finalHistologi = $this->processFinalCheckboxState($histologi, $selectedHisto, $unselectedHisto);
        $finalImunohistokimia = $this->processFinalCheckboxState($imunohistokimia, $selectedImuno, $unselectedImuno);


        // Insert main data into verifikasi_bpjs table
        $dataVerifikasiAwal = [
            'id' => $id_verifikasi,
            'norm' => $norm,
            'no_kartu_bpjs' => $no_bpjs,
            'nama_pasien' => $nama_lengkap,
            'created_by' => $this->session->userdata('id'),
            'status' => 1
        ];
        if ($user_exist != 1) {
            $this->db->insert('bpjs.verifikasi_awal', $dataVerifikasiAwal);
        } else {
            $verifikasiData = $this->VerifBpjsModel->get_verifikasi_awal($norm);
            $id_verifikasi = $verifikasiData[0]['id'];
        }

        $dataVlInsert = [
            'id' => $id_layanan,
            'id_verifikasi_awal' => $id_verifikasi,
            'tanggal_pelayanan' => $tgl_layanan,
            'tanggal_jatuh_tempo' => $tgl_jatuh_tempo,
            'tinggi_badan' => floatval(str_replace(',', '.', $post['tinggiBadan'])),
            'berat_badan' => floatval(str_replace(',', '.', $post['beratBadan'])),
            'created_by' => $this->session->userdata('id'),
            'status' => 1
        ];

        $dataVlUpdate = [
            'id_verifikasi_awal' => $id_verifikasi,
            'tanggal_pelayanan' => $tgl_layanan,
            'tanggal_jatuh_tempo' => $tgl_jatuh_tempo,
            'tinggi_badan' => floatval(str_replace(',', '.', $post['tinggiBadan'])),
            'berat_badan' => floatval(str_replace(',', '.', $post['beratBadan'])),
            'updated_by' => $this->session->userdata('id'),
            'status' => 1
        ];

        if ($id_layanan_exist == null) {
            $this->db->insert('bpjs.verifikasi_layanan', $dataVlInsert);
        } else {
            $this->VerifBpjsModel->updateLayanan($id_layanan, $dataVlUpdate);
        }

        // Get the current date in your desired format (e.g., Y-m-d)
        $currentDate = date('Y-m-d');

        // Handle JPOK Upload
        if (!$this->uploadFile('jpok', "./uploads/jpok/{$currentDate}/", 1, $id_verifikasi))
            return;

        // Handle IHK Eksternal Upload
        if (!$this->uploadFile('extern_ihk', "./uploads/ihk/{$currentDate}/", 3, $id_verifikasi))
            return;

        // Handle Radiologi Eksternal Upload
        if (!$this->uploadFile('extern_radiologi', "./uploads/radiologi/{$currentDate}/", 5, $id_verifikasi))
            return;

        // Handle PA Eksternal Upload
        if (!$this->uploadFile('extern_pa', "./uploads/pa/{$currentDate}/", 2, $id_verifikasi))
            return;

        // Handle BMP Eksternal Upload
        if (!$this->uploadFile('extern_bmp', "./uploads/bmp/{$currentDate}/", 4, $id_verifikasi))
            return;

        // Delete existing records from verif_resep table

        // Delete records with the same id_layanan

        if (!empty($id_layanan)) {
            $this->db->where('id_layanan', $id_layanan);
            $this->db->delete('bpjs.verif_resep');

            $this->db->where('id_verifikasi', $id_layanan);
            $this->db->where('jenis', 2);
            $this->db->delete('bpjs.verif_radiologi');

            // Delete records with the same id_layanan
            $this->db->where('id_layanan', $id_layanan);
            $this->db->delete('bpjs.verif_pk');
        }
        // Insert new records into verif_resep table
        if (!empty($finalResep)) :
            foreach ($finalResep as $res) {
                $dataResep = [
                    'id_layanan' => $id_layanan,
                    'id_resep' => $res,
                    'created_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_resep', $dataResep)) {
                    echo json_encode(['status' => 'error', 'message' => 'Failed to submit data for resep.']);
                    return;
                }
            }
        endif;


        // Delete existing records from verif_pk table
        if (!empty($finalPK)) {
            // Insert new records into verif_pk table
            foreach ($finalPK as $pk) {
                $dataPk = [
                    'id_layanan' => $id_layanan,
                    'id_kunjungan' => $pk,
                    'created_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_pk', $dataPk)) {
                    echo json_encode(['status' => 'error', 'message' => 'Failed to submit data for pk.']);
                    return;
                }
            }
        }

        $response = $this->insertVerifOldData($inputOldData, $id_verifikasi);
        if ($response['status'] === 'error') {
            echo json_encode($response);
            return;
        }

        $response = $this->insertVerificationData($finalSitologi, 1, $id_verifikasi);
        if ($response['status'] === 'error') {
            echo json_encode($response);
            return;
        }

        $response = $this->insertVerificationData($finalHistologi, 2, $id_verifikasi);
        if ($response['status'] === 'error') {
            echo json_encode($response);
            return;
        }

        $response = $this->insertVerificationData($finalImunohistokimia, 3, $id_verifikasi);
        if ($response['status'] === 'error') {
            echo json_encode($response);
            return;
        }
        $response = $this->insertVerificationData($molekuler, 4, $id_verifikasi);
        if ($response['status'] === 'error') {
            echo json_encode($response);
            return;
        }

        // 
        // First, delete existing records that match the criteria
        $this->db->where('id_verifikasi', $id_verifikasi);
        $this->db->where('jenis', 1);
        $this->db->delete('bpjs.verif_radiologi');
        if (!empty($radiologi_verif)) {
            foreach ($radiologi_verif as $radVerif) {
                $dataRadVerif = [
                    'id_verifikasi' => $id_verifikasi,
                    'id_tindakan_medis' => $radVerif,
                    'jenis' => 1,
                    'created_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_radiologi', $dataRadVerif)) {
                    echo json_encode(['status' => 'error', 'message' => 'Failed to submit data for radiologi verif.']);
                    return;
                }
            }
        }

        // Insert into verif_radiologi_layanan table

        if (!empty($radiologi_layanan)) {
            foreach ($radiologi_layanan as $radLayanan) {
                $dataRadLayanan = [
                    'id_verifikasi' => $id_layanan,
                    'id_tindakan_medis' => $radLayanan,
                    'jenis' => 2,
                    'created_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_radiologi', $dataRadLayanan)) {
                    echo json_encode(['status' => 'error', 'message' => 'Failed to submit data for radiologi layanan.']);
                    return;
                }
            }
        }

        // Insert other documents if provided
        if (!empty($_FILES['other_docs']['name'][0])) {
            $otherDocs = $_FILES['other_docs'];
            $otherDocsPaths = []; // Array to store file paths

            // Check if the directory exists, if not create it
            $otherDocsConfig['upload_path'] = "./uploads/other_docs/";
            if (!is_dir($otherDocsConfig['upload_path'])) {
                mkdir($otherDocsConfig['upload_path'], 0755, true); // Create directory if it doesn't exist
            }

            for ($i = 0; $i < count($otherDocs['name']); $i++) {
                $_FILES['file']['name'] = $otherDocs['name'][$i];
                $_FILES['file']['type'] = $otherDocs['type'][$i];
                $_FILES['file']['tmp_name'] = $otherDocs['tmp_name'][$i];
                $_FILES['file']['error'] = $otherDocs['error'][$i];
                $_FILES['file']['size'] = $otherDocs['size'][$i];

                $otherDocsConfig['allowed_types'] = 'doc|docx|pdf|jpg|jpeg|png';
                $otherDocsConfig['max_size'] = 2048; // Size in KB
                $otherDocsConfig['file_name'] = uniqid(); // Unique file name to prevent collisions
                $this->upload->initialize($otherDocsConfig);

                if ($this->upload->do_upload('file')) {
                    $otherDocsData = $this->upload->data();
                    $filePath = $otherDocsConfig['upload_path'];
                    $fileName = $otherDocsData['file_name'];
                    $otherDocsPaths[] = $otherDocsData['file_name']; // Save file paths for database

                    $insertOtherFiles = [
                        'id_verifikasi' => $id_verifikasi, // Replace with actual verification ID
                        'file_path' => $filePath,
                        'file_name' => $fileName,
                        'file_category' => 0,
                        'created_by' => $this->session->userdata('id'),
                        'status' => 1 // Adjust as per your status requirements
                    ];

                    // Insert into the database
                    $this->db->insert('bpjs.verif_files', $insertOtherFiles);
                } else {
                    $response = [
                        'status' => 'success',
                        'message' => 'Data successfully submitted!'
                    ];
                    echo json_encode($response);
                    return;
                }
            }
        }

        $this->db->trans_complete(); // Commit atau rollback otomatis berdasarkan status transaksi

        if ($this->db->trans_status() === false) {
            $response = [
                'status' => 'error',
                'message' => 'Data submission failed. Please try again.'
            ];
        } else {
            $response = [
                'status' => 'success',
                'message' => 'Data successfully submitted!'
            ];
        }

        echo json_encode($response);
    }

    /**
     * Process final checkbox state based on user interactions
     * @param array $originalData - Original data from form checkboxes
     * @param array $selectedArray - Array of explicitly selected items
     * @param array $unselectedArray - Array of explicitly unselected items
     * @return array - Final array of selected items
     */
    private function processFinalCheckboxState($originalData, $selectedArray, $unselectedArray)
    {
        // Start with original data (items that were checked by default)
        $finalData = $originalData ?: [];

        // Add explicitly selected items
        foreach ($selectedArray as $item) {
            if (!in_array($item, $finalData)) {
                $finalData[] = $item;
            }
        }

        // Remove explicitly unselected items
        $finalData = array_filter($finalData, function($item) use ($unselectedArray) {
            return !in_array($item, $unselectedArray);
        });

        // Re-index array to ensure clean numeric indices
        return array_values($finalData);
    }

    private function insertVerificationData($dataArray, $jenis, $id_verifikasi)
    {
        // Delete existing records where jenis and id_verifikasi match
        $this->db->where('jenis', $jenis);
        $this->db->where('id_verifikasi', $id_verifikasi);
        $this->db->delete('bpjs.verif_pa');

        // Insert new records if dataArray is not empty
        if (!empty($dataArray)) {
            foreach ($dataArray as $data) {
                $dataToInsert = [
                    'id_verifikasi' => $id_verifikasi,
                    'id_hasil_pa' => $data,
                    'jenis' => $jenis,
                    'created_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_pa', $dataToInsert)) {
                    return ['status' => 'error', 'message' => 'Failed to submit data for jenis ' . $jenis . '.'];
                }
            }
        }

        return ['status' => 'success'];
    }

    private function insertVerifOldData($dataArray, $id_verifikasi)
    {
        // Delete existing records where jenis and id_verifikasi match
        $this->db->where('id_verifikasi', $id_verifikasi);
        $this->db->delete('bpjs.verif_uploads_bpjs');

        // Insert new records if dataArray is not empty
        if (!empty($dataArray)) {
            foreach ($dataArray as $data) {
                $dataToInsert = [
                    'id_verifikasi' => $id_verifikasi,
                    'id_uploads_bpjs' => $data,
                    'updated_by' => $this->session->userdata('id'),
                    'status' => 1
                ];
                if (!$this->db->insert('bpjs.verif_uploads_bpjs', $dataToInsert)) {
                    return ['status' => 'error', 'message' => 'Failed to submit data'];
                }
            }
        }

        return ['status' => 'success'];
    }

    private function uploadFile($field_name, $upload_path, $file_category, $id_verifikasi)
    {
        // Check if file is uploaded
        if (empty($_FILES[$field_name]['name'])) {
            return true; // Skip if no file uploaded
        }

        // Set up the upload configuration
        $config['upload_path'] = $upload_path;
        $config['allowed_types'] = 'doc|docx|pdf|jpg|jpeg|png';
        $config['max_size'] = 2048; // 2MB
        $config['file_name'] = uniqid();

        // Create directory if it doesn't exist
        if (!is_dir($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        $this->upload->initialize($config);

        // Perform upload
        if ($this->upload->do_upload($field_name)) {
            $fileData = $this->upload->data();
            $dataFile = [
                'id_verifikasi' => $id_verifikasi,
                'file_name' => $fileData['file_name'],
                'file_path' => $config['upload_path'],
                'file_category' => $file_category,
                'status' => 1,
                'created_by' => $this->session->userdata('id')
            ];
            $this->db->insert('bpjs.verif_files', $dataFile);
            return true;
        } else {
            $response = [
                'status' => 'error',
                'message' => ucfirst($field_name) . ' Upload Error: ' . $this->upload->display_errors('', '')
            ];
            echo json_encode($response);
            return false;
        }
    }

    private function uniqid_int()
    {
        // Mengambil waktu dalam detik, memastikan maksimal 10 digit
        $timestamp = time();

        // Menghasilkan angka acak 4 digit untuk menambah entropi
        $randomNumber = mt_rand(1000, 9999);

        // Menggabungkan timestamp dan angka acak, lalu memotong jika lebih dari 10 digit
        $uniqueId = substr($timestamp . $randomNumber, 0, 10);

        return (int) $uniqueId;
    }

    public function tableDocuments()
    {
        $norm = $this->input->get('norm');
        $limit = $this->input->get('length'); // Number of records per page
        $offset = $this->input->get('start'); // Record number to start from
        $akses = $this->session->userdata('type');

        // Fetch documents with pagination
        $documents = $this->VerifBpjsModel->get_document($norm, $limit, $offset);

        // Get total number of records
        $totalRecords = $this->VerifBpjsModel->count_all_documents();

        // Prepare the mapping for file categories
        $categoryMapping = [
            0 => 'Dokumen Lainnya',
            1 => 'Dokumen JPOK',
            2 => 'Dokumen PA',
            3 => 'Dokumen IHK',
            4 => 'Dokumen BMP',
            5 => 'Dokumen Radiologi'
        ];

        // Prepare data for DataTable
        $data = [];
        foreach ($documents as $document) {
            // Translate file_category to its corresponding string
            $fileCategory = isset($categoryMapping[$document->file_category]) ? $categoryMapping[$document->file_category] : 'Unknown Category';

            // Initialize actions with the 'Lihat' button including Font Awesome icon
            $actions = '<a href="' . base_url($document->file_path . $document->file_name) . '" class="btn btn-primary" target="_blank"><i class="fas fa-eye"></i> Lihat</a>';

            // Add delete button only if access type is not "BPJS"
            if ($akses != "BPJS") {
                $actions .= '<a class="btn btn-danger delete-btn" data-id="' . $document->id . '"><i class="fas fa-trash-alt"></i> Hapus</a>';
            }

            $data[] = [
                'file_category' => $fileCategory, // Use the translated category
                'file_name' => $document->file_name,
                'created_at' => date('d/m/Y H:i:s', strtotime($document->created_at)),
                'actions' => $actions // Use the constructed actions
            ];
        }

        // Return JSON response
        echo json_encode([
            'draw' => intval($this->input->get('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords, // Assuming filtering logic not yet implemented
            'data' => $data
        ]);
    }

    public function hapusLayanan()
    {
        $idLayanan = $this->input->post('idLayanan');
        $data = [
            'status' => 0
        ];
        if ($this->VerifBpjsModel->updateLayanan($idLayanan, $data)) {
            echo 1; // Success response
        } else {
            echo 0; // Failure response
        }
    }

    public function deleteFile()
    {
        $id_file = $this->input->post('id_file');
        $data = [
            'status' => 0 // Assuming 0 means the file is deleted
        ];

        if ($this->VerifBpjsModel->updateFiles($id_file, $data)) {
            // Success response
            echo json_encode(['status' => 'success', 'message' => 'File berhasil dihapus.']);
        } else {
            // Failure response
            echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus file.']);
        }
    }

    public function onedrive()
    {
        $session = $this->session->get_userdata();
        if ($session['type'] == 'BPJS') {
            redirect('dashboard');
        }
        $data = [
            'title'         => 'Farmasi One Drive | VBPJS RSKD',
            'header'        => 'Farmasi One Drive',
            'page_active'   => 'farmasiOnedrive',
            'isi'           => 'farmasi/onedrive',
            'session'       => $session,
            'csrf_token'    => $this->security->get_csrf_token_name(),
            'csrf_hash'     => $this->security->get_csrf_hash(),
        ];
        $this->load->view('layout/wrapper', $data);
    }

    public function getOnedriveData()
    {
        $tgl_mulai = $this->input->get('tgl_mulai') ?: date('Y-m-d', strtotime('-1 month'));
        $tgl_akhir = $this->input->get('tgl_akhir') ?: date('Y-m-d');
        $kategori = $this->input->get('kategori') ?: 0;

        $this->load->model('Farmasi/FarmasiModel');
        $result = $this->FarmasiModel->getOnedriveData($tgl_mulai, $tgl_akhir, $kategori);

        // $data = [];        
        // foreach ($result as $row) {
        //     $data[] = array(
        //         $row->tanggal_pelayanan,
        //         $row->tanggal_jatuh_tempo,
        //         $row->norm,
        //         $row->nama_pasien,
        //         $row->no_kartu_bpjs,
        //         $row->sep_resep,
        //         $row->jenis_obat,
        //         '<div class="btn-group" role="group">' .
        //         '<button class="btn btn-primary btn-sm cetak-new" data-id="' . $row->id_vl . '" data-sep="' . $row->sep_resep . '"><i class="fas fa-eye"></i> Preview</button>' . 
        //         '<button class="btn btn-success btn-sm cetak-langsung ml-1" data-id="' . $row->id_vl . '" data-sep="' . $row->sep_resep . '"><i class="fas fa-print"></i> Cetak </button>' .
        //         '</div>'
        //     );
        // }

        echo json_encode([
            'data' => $result
        ]);
    }
    // public function getOnedriveData()
    // {
    //     $tgl_mulai = $this->input->get('tgl_mulai') ?: date('Y-m-d', strtotime('-1 month'));
    //     $tgl_akhir = $this->input->get('tgl_akhir') ?: date('Y-m-d');
    //     $kategori = $this->input->get('kategori') ?: 0;

    //     $this->load->model('Farmasi/FarmasiModel');
    //     $data = $this->FarmasiModel->getOnedriveData($tgl_mulai, $tgl_akhir, $kategori);

    //     echo json_encode([
    //         'data' => $data
    //     ]);
    // }
    // ... existing code ...

    public function compilePdf($id_verifikasi)
    {
        // Load the database and get file paths
        $this->load->database();

        $query = $this->db->query("
        SELECT CONCAT(vf.file_path, vf.file_name) AS file_location
        FROM bpjs.verif_files vf
        WHERE vf.id_verifikasi = ?
        UNION ALL
        SELECT CONCAT('./uploads/', ub.file) AS file_location
        FROM bpjs.uploads_bpjs ub
        JOIN bpjs.verif_uploads_bpjs vub ON ub.id = vub.id_uploads_bpjs
        WHERE vub.id_verifikasi = ?
    ", [$id_verifikasi, $id_verifikasi]);

        $files = $query->result_array();

        // Load FPDI
        require_once APPPATH . 'third_party/fpdi/src/autoload.php';

        $pdf = new \setasign\Fpdi\Fpdi();

        foreach ($files as $file) {
            $filePath = $file['file_location'];

            // Get file extension
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);

            if (strtolower($ext) === 'pdf') {
                // Add PDF page
                $pageCount = $pdf->setSourceFile($filePath);
                for ($i = 1; $i <= $pageCount; $i++) {
                    $template = $pdf->importPage($i);
                    $size = $pdf->getTemplateSize($template);
                    $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                    $pdf->useTemplate($template);
                }
            } else {
                // Add image page
                $pdf->AddPage();
                $pdf->Image($filePath, 10, 10, 190);
            }
        }

        // Output the combined PDF
        $outputPath = APPPATH . '../uploads/compiled/' . $id_verifikasi . '.pdf';
        $pdf->Output($outputPath, 'F');

        // Send to browser
        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="compiled.pdf"');
        readfile($outputPath);
    }

    // ... existing code ...

    // ... existing code ...

    public function downloadPdfDirect()
    {
        $id = $this->input->get('id');
        $sep = $this->input->get('sep');

        // URL API untuk mengambil PDF
        $apiUrl = 'http://192.168.7.60/vbpjsCompiler/compilePdf/' . $id . '?sep=' . urlencode($sep);

        // Konfigurasi CURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Eksekusi CURL
        $pdfContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $pdfContent) {
            // Set header untuk download
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="SEP_' . trim($sep) . '.pdf"');
            header('Content-Length: ' . strlen($pdfContent));
            header('Cache-Control: private, max-age=0, must-revalidate');
            header('Pragma: public');

            echo $pdfContent;
            exit;
        } else {
            // Jika gagal mengambil PDF
            header('HTTP/1.0 404 Not Found');
            echo json_encode(['error' => 'File tidak ditemukan']);
        }
    }

    // ... existing code ...

    public function indexFarmasiV2()
    {
        if ($this->session->userdata('type') == 'BPJS') {
            redirect('dashboard');
        }
        $data = [
            'title'         => 'Farmasi One Drive | VBPJS RSKD',
            'header'        => 'Farmasi One Drive',
            'page_active'   => 'farmasiOnedriveV2',
            'isi'           => 'farmasi/onedriveV2',
            'session'       => $this->session->get_userdata(),
            'csrf_token'    => $this->security->get_csrf_token_name(),
            'csrf_hash'     => $this->security->get_csrf_hash(),
        ];
        $this->load->view('layout/wrapper', $data);
    }
    public function getOnedriveDataV2()
    {
        $tgl_mulai = $this->input->get('tgl_mulai') ?: date('Y-m-d', strtotime('-1 month'));
        $tgl_akhir = $this->input->get('tgl_akhir') ?: date('Y-m-d');
        $kategori = $this->input->get('kategori') ?: 0;

        $this->load->model('Farmasi/FarmasiModel');
        $result = $this->FarmasiModel->getOnedriveDataV2($tgl_mulai, $tgl_akhir, $kategori);

        echo json_encode([
            'data' => $result
        ]);
    }
    public function formDataV2()
    {
        $norm = $this->input->get('norm');
        $tgl_awal = $this->input->get('tgl_awal');
        $tgl_akhir = $this->input->get('tgl_akhir');
        $kategori = $this->input->get('kategori');

        $data_pasien = $this->FarmasiModel->get_data_pasien($norm);
        $verif_exist = $this->VerifBpjsModel->get_verifikasi_awal($norm);
        $file_exist = $this->VerifBpjsModel->get_verif_files(false, $norm);
        $jpok_exist = null;

        foreach ($file_exist as $file) {
            if ($file->file_category == 1) {
                $jpok_exist = $file;
                break;
            }
        }

        if (!$norm || !$data_pasien) {
            redirect('Farmasi/indexFarmasiV2?tgl_awal=' . $tgl_awal . '&tgl_akhir=' . $tgl_akhir . '&kategori=' . $kategori);
        }

        $data = [
            'title' => 'Form Input V2 | VBPJS RSKD',
            'header' => 'Form Input V2',
            'isi' => 'farmasi/formulirV2',
            'page' => 'farmasi-v2',
            'session' => $this->session->get_userdata(),
            'verif_exist' => $verif_exist,
            'file_exist' => $file_exist,
            'jpok_exist' => $jpok_exist,
            'norm' => $norm,
            'tgl_awal' => $tgl_awal,
            'tgl_akhir' => $tgl_akhir,
            'kategori' => $kategori,
            'csrf_token' => $this->security->get_csrf_token_name(),
            'csrf_hash' => $this->security->get_csrf_hash(),
            'data_pasien' => $data_pasien
        ];

        $this->load->view('layout/wrapper', $data);
    }
}


/* End of File Farmasi.php */
/* Location: ./application/controllers/Farmasi.php */